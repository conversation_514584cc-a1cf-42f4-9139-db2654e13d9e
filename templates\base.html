<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Calculette Mauricette - Calculatrice d'Heures de Travail Gratuite en Ligne{% endblock %}</title>
    <meta name="description" content="{% block description %}Calculette Mauricette gratuite pour calculer vos heures de travail avec pause. Calculatrice en ligne simple et efficace pour le calcul des minutes et heures.{% endblock %}">
    <meta name="keywords" content="calculette mauricette, calculatrice heure, calcul minutes, calculette gratuit, calculatrice travail">
    <meta name="author" content="Calculette Mauricette">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://calculette-mauricette.fr/">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">

    {% block head %}{% endblock %}
    {% block extra_css %}{% endblock %}

    <!-- Schema.org structured data -->
    {% block schema_data %}
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Calculette Mauricette",
      "description": "Calculatrice d'heures de travail gratuite en ligne",
      "url": "https://calculette-mauricette.fr",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "EUR"
      },
      "featureList": [
        "Calcul d'heures de travail",
        "Gestion des pauses",
        "Calcul hebdomadaire",
        "Templates Excel"
      ],
      "author": {
        "@type": "Organization",
        "name": "Calculette Mauricette",
        "url": "https://calculette-mauricette.fr"
      }
    }
    </script>
    {% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-clock me-2 text-primary"></i>
                <span>Calculette Mauricette</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="fas fa-home me-1"></i> Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/calculette-heure"><i class="fas fa-clock me-1"></i> Calculette Heure</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-calendar-week me-1"></i> Calculette Semaine</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-calendar-alt me-1"></i> Calculette Mois</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-file-excel me-1"></i> Excel Templates</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-question-circle me-1"></i> Guide d'utilisation</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="py-5 bg-dark">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 mb-4">
                    <div class="h4 text-white mb-3"><i class="fas fa-clock me-2"></i> Calculette Mauricette</div>
                    <p class="text-secondary">Votre calculatrice d'heure de travail gratuite et fiable. Calculez vos heures de travail avec pause en quelques clics !</p>
                    <p class="text-secondary small">© 2025 Calculette Mauricette - Tous droits réservés</p>
                </div>

                <div class="col-lg-9">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <h5 class="text-white mb-3">Outils Spécialisés</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Calculette Mauricette Heure</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Calculette Mauricette Semaine</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Calculette Mauricette Mois</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Calculette Mauricette Excel</a></li>
                            </ul>
                        </div>

                        <div class="col-md-4 mb-4">
                            <h5 class="text-white mb-3">Ressources</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Guide Calculette Mauricette</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Calculatrice en ligne</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">FAQ</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Blog</a></li>
                            </ul>
                        </div>

                        <div class="col-md-4 mb-4">
                            <h5 class="text-white mb-3">À propos</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Contact</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Mentions légales</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Politique de confidentialité</a></li>
                                <li class="mb-2"><a href="#" class="text-decoration-none text-secondary">Plan du site</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="border-secondary my-4">

            <div class="text-center">
                <p class="text-secondary small">
                    <strong>Calculette Mauricette</strong> - calculette mauricette, calculatrice heure, calcul minutes, calculette gratuit, calculatrice travail, calculette mauricette semaine, calculette mauricette 2025
                </p>
                <p class="text-secondary small">
                    Les calculs fournis par la Calculette Mauricette sont donnés à titre indicatif. Vérifiez toujours vos calculs d'heures de travail avec votre employeur.
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 