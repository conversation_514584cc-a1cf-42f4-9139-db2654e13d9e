from flask import Flask, render_template

app = Flask(__name__)


@app.route('/')
def index():
    return render_template('index.html')


@app.route('/calculette-heure')
def calculette_heure():
    return render_template('calculette_heure.html')


@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404


if __name__ == '__main__':
    print("Starting Calculette Mauricette server...")
    app.run(debug=True, host='0.0.0.0', port=8000)
